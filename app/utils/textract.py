"""
AWS Textract utility functions for document analysis and OCR.

This module provides production-ready Textract functionality for:
- Document analysis with OCR
- Text extraction from various document types
- Async operations for scalability
- Comprehensive logging for AWS environments
"""

import boto3
import asyncio
import logging
from datetime import datetime
from typing import Dict, Any, Optional
from botocore.exceptions import ClientError
from app.core.configuration import settings

# Configure logging
logger = logging.getLogger(__name__)


class TextractProcessor:
    """
    AWS Textract processor for document analysis and text extraction.
    """
    
    def __init__(self, aws_region: str, aws_access_key_id: str, aws_secret_access_key: str):
        """
        Initialize Textract processor.
        
        Args:
            aws_region: AWS region
            aws_access_key_id: AWS access key ID
            aws_secret_access_key: AWS secret access key
        """
        logger.info("🚀 Initializing TextractProcessor...")
        
        try:
            self.textract_client = boto3.client(
                'textract',
                region_name=aws_region,
                aws_access_key_id=aws_access_key_id,
                aws_secret_access_key=aws_secret_access_key
            )
            
            self.region = aws_region
            
            logger.info("✅ Successfully initialized TextractProcessor")
            logger.info(f"   AWS Region: {self.region}")
            logger.info(f"   Timestamp: {datetime.now().isoformat()}")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize TextractProcessor: {str(e)}")
            logger.error(f"   Error type: {type(e).__name__}")
            logger.error(f"   Timestamp: {datetime.now().isoformat()}")
            raise
    
    async def analyze_document(self, bucket_name: str, object_key: str) -> Dict[str, Any]:
        """
        Analyze document using AWS Textract for OCR and document analysis.
        
        Args:
            bucket_name: S3 bucket name
            object_key: S3 object key
            
        Returns:
            dict: Textract analysis results
        """
        logger.info(f"📄 Starting Textract document analysis...")
        logger.info(f"   S3 Location: s3://{bucket_name}/{object_key}")
        logger.info(f"   Timestamp: {datetime.now().isoformat()}")
        
        try:
            # Start document analysis
            response = self.textract_client.start_document_analysis(
                DocumentLocation={
                    'S3Object': {
                        'Bucket': bucket_name,
                        'Name': object_key
                    }
                },
                FeatureTypes=['TABLES', 'FORMS', 'LAYOUT']
            )
            
            job_id = response['JobId']
            logger.info(f"✅ Textract analysis started successfully")
            logger.info(f"   Job ID: {job_id}")
            
            # Poll for completion
            logger.info("⏳ Waiting for Textract analysis to complete...")
            poll_count = 0
            start_time = datetime.now()
            
            while True:
                poll_count += 1
                elapsed_time = (datetime.now() - start_time).total_seconds()
                
                result = self.textract_client.get_document_analysis(JobId=job_id)
                status = result['JobStatus']
                
                logger.info(f"   Poll #{poll_count} - Status: {status} (Elapsed: {elapsed_time:.1f}s)")
                
                if status == 'SUCCEEDED':
                    logger.info(f"✅ Textract analysis completed successfully!")
                    logger.info(f"   Total processing time: {elapsed_time:.1f} seconds")
                    logger.info(f"   Total polls: {poll_count}")
                    logger.info(f"   Pages analyzed: {len(result.get('Blocks', []))}")
                    return result
                    
                elif status == 'FAILED':
                    error_msg = f"Textract analysis failed after {elapsed_time:.1f} seconds"
                    logger.error(f"❌ {error_msg}")
                    logger.error(f"   Job ID: {job_id}")
                    raise Exception(error_msg)
                    
                elif status in ['IN_PROGRESS']:
                    logger.debug(f"   ⏳ Analysis still in progress... waiting 5 seconds")
                    await asyncio.sleep(5)
                else:
                    logger.warning(f"   ⚠️  Unknown status: {status}")
                    await asyncio.sleep(5)
                    
        except ClientError as e:
            error_code = e.response['Error']['Code']
            error_message = e.response['Error']['Message']
            logger.error(f"❌ Textract ClientError: {error_code} - {error_message}")
            logger.error(f"   S3 Location: s3://{bucket_name}/{object_key}")
            logger.error(f"   Timestamp: {datetime.now().isoformat()}")
            raise
            
        except Exception as e:
            logger.error(f"❌ Unexpected error during Textract analysis: {str(e)}")
            logger.error(f"   Error type: {type(e).__name__}")
            logger.error(f"   S3 Location: s3://{bucket_name}/{object_key}")
            logger.error(f"   Timestamp: {datetime.now().isoformat()}")
            raise
    
    def extract_text_from_result(self, textract_result: Dict[str, Any]) -> str:
        """
        Extract clean text from Textract analysis result.
        
        Args:
            textract_result: Result from Textract analysis
            
        Returns:
            str: Extracted text content
        """
        logger.info("📝 Extracting text from Textract results...")
        
        try:
            blocks = textract_result.get('Blocks', [])
            text_blocks = [block for block in blocks if block['BlockType'] == 'LINE']
            
            extracted_text = '\n'.join([block.get('Text', '') for block in text_blocks])
            
            logger.info(f"✅ Text extraction completed")
            logger.info(f"   Total blocks processed: {len(blocks)}")
            logger.info(f"   Text lines extracted: {len(text_blocks)}")
            logger.info(f"   Total characters: {len(extracted_text)}")
            
            return extracted_text
            
        except Exception as e:
            logger.error(f"❌ Error extracting text from Textract result: {str(e)}")
            logger.error(f"   Error type: {type(e).__name__}")
            logger.error(f"   Timestamp: {datetime.now().isoformat()}")
            raise
    
    async def process_document(self, bucket_name: str, object_key: str) -> Dict[str, Any]:
        """
        Complete Textract processing: analyze document and extract text.
        
        Args:
            bucket_name: S3 bucket name
            object_key: S3 object key
            
        Returns:
            dict: Complete Textract processing results
        """
        logger.info("🎯 Starting complete Textract processing...")
        logger.info(f"   S3 Location: s3://{bucket_name}/{object_key}")
        
        start_time = datetime.now()
        
        try:
            # Analyze document
            textract_result = await self.analyze_document(bucket_name, object_key)
            
            # Extract text
            extracted_text = self.extract_text_from_result(textract_result)
            
            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds()
            
            result = {
                "textract_metadata": {
                    "s3_location": f"s3://{bucket_name}/{object_key}",
                    "processing_start_time": start_time.isoformat(),
                    "processing_end_time": end_time.isoformat(),
                    "total_processing_time_seconds": processing_time,
                    "textract_blocks_count": len(textract_result.get('Blocks', [])),
                    "extracted_text_length": len(extracted_text),
                    "aws_region": self.region
                },
                "textract_raw_result": textract_result,
                "extracted_text": extracted_text
            }
            
            logger.info("✅ Textract processing completed successfully!")
            logger.info(f"   Total processing time: {processing_time:.2f} seconds")
            
            return result
            
        except Exception as e:
            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds()
            
            logger.error("❌ Textract processing failed!")
            logger.error(f"   Error: {str(e)}")
            logger.error(f"   Error type: {type(e).__name__}")
            logger.error(f"   Processing time before failure: {processing_time:.2f} seconds")
            logger.error(f"   Failure timestamp: {end_time.isoformat()}")
            raise


# Utility functions for easy access
async def analyze_document_with_textract(bucket_name: str, object_key: str, 
                                       aws_region: str = None, 
                                       aws_access_key_id: str = None, 
                                       aws_secret_access_key: str = None) -> Dict[str, Any]:
    """
    Analyze document using Textract.
    
    Args:
        bucket_name: S3 bucket name
        object_key: S3 object key
        aws_region: AWS region (defaults to settings)
        aws_access_key_id: AWS access key (defaults to settings)
        aws_secret_access_key: AWS secret key (defaults to settings)
        
    Returns:
        dict: Textract processing results
    """
    # Use provided credentials or fall back to settings
    region = aws_region or settings.AWS_REGION
    access_key = aws_access_key_id or settings.AWS_ACCESS_KEY_ID
    secret_key = aws_secret_access_key or settings.AWS_SECRET_ACCESS_KEY
    
    processor = TextractProcessor(region, access_key, secret_key)
    return await processor.process_document(bucket_name, object_key)
